@extends('layouts.app')

@section('title', 'หน้าแรก - ' . (isset($siteSettings) ? $siteSettings->site_name : 'SoloShop'))

@section('content')
<!-- Hero Section -->
<section class="hero-section text-white">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6">
                <h1 class="display-4 fw-bold mb-4">
                    @if(isset($siteSettings) && $siteSettings->hero_icon)
                        <img src="{{ \App\Helpers\ImageHelper::getImageUrl($siteSettings->hero_icon) }}"
                             alt="Hero Icon" class="me-3" style="height: 60px; width: auto;">
                    @else
                        <i class="fas fa-shopping-cart me-3"></i>
                    @endif
                    {{ isset($siteSettings) && $siteSettings->hero_title ? $siteSettings->hero_title : 'ยินดีต้อนรับสู่ SoloShop' }}
                </h1>
                <p class="lead mb-4">{{ isset($siteSettings) && $siteSettings->site_description ? $siteSettings->site_description : 'แพลตฟอร์มการขายออนไลน์ที่ครบครันสำหรับธุรกิจของคุณ' }}</p>
                <div class="d-flex gap-3 flex-wrap">
                    <a href="{{ route('services.index') }}" class="btn btn-light btn-lg">
                        <i class="fas fa-tools me-2"></i>ดูบริการ
                    </a>
                    <a href="{{ route('packages.index') }}" class="btn btn-outline-light btn-lg">
                        <i class="fas fa-box me-2"></i>ดูแพ็คเกจ
                    </a>
                    <a href="{{ route('activities.index') }}" class="btn btn-outline-light btn-lg">
                        <i class="fas fa-images me-2"></i>ดูกิจกรรม
                    </a>
                </div>
            </div>
            <div class="col-lg-6 text-center">
                @if(isset($siteSettings) && $siteSettings->hero_icon)
                    <img src="{{ \App\Helpers\ImageHelper::getImageUrl($siteSettings->hero_icon) }}"
                         alt="Hero Icon" class="opacity-75 floating-animation" style="max-height: 300px; width: auto;">
                @else
                    <i class="fas fa-shopping-cart fa-10x opacity-75 floating-animation"></i>
                @endif
            </div>
        </div>
    </div>
</section>

<!-- Gallery Section -->
@if($contents && $contents->count() > 0)
    <section class="py-5">
        <div class="container">
            <div class="text-center mb-5">
                <h2 class="section-title">แกลเลอรี่ภาพ</h2>
                <p class="text-muted lead">ภาพความทรงจำและช่วงเวลาสำคัญ</p>
            </div>
            <div class="row g-4">
                @foreach($contents as $content)
                <div class="col-lg-4 col-md-6">
                    <div class="gallery-item">
                        <div class="gallery-image-container">
                            <img src="{{ \App\Helpers\ImageHelper::getImageUrl($content->image) }}"
                                 class="gallery-image" alt="{{ $content->title }}"
                                 data-bs-toggle="modal" data-bs-target="#galleryModal{{ $loop->index }}">
                            <div class="gallery-overlay">
                                <div class="gallery-overlay-content">
                                    <h5 class="text-white mb-2">{{ $content->title }}</h5>
                                    <p class="text-white-50 mb-0">{{ Str::limit($content->content, 80) }}</p>
                                    <i class="fas fa-search-plus fa-2x text-white mt-3"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                @endforeach
            </div>
        </div>
    </section>

    <!-- Gallery Modals -->
    @foreach($contents as $content)
    <div class="modal fade" id="galleryModal{{ $loop->index }}" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-lg modal-dialog-centered">
            <div class="modal-content bg-transparent border-0">
                <div class="modal-header border-0">
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body text-center p-0">
                    <img src="{{ \App\Helpers\ImageHelper::getImageUrl($content->image) }}"
                         class="img-fluid rounded" alt="{{ $content->title }}"
                         style="max-height: 80vh; width: auto;">
                    <div class="mt-3 text-white">
                        <h4>{{ $content->title }}</h4>
                        <p class="text-white-50">{{ $content->content }}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @endforeach
@endif

<!-- Features Section -->
<section class="py-5 bg-light">
    <div class="container">
        <div class="text-center mb-5">
            <h2 class="section-title">คุณค่าที่เราให้ความสำคัญ</h2>
            <p class="text-muted lead">ด้วยความเคารพและเกียรติที่เราให้แก่ทุกท่าน</p>
        </div>
        <div class="row g-4">
            <div class="col-md-4">
                <div class="card h-100 shadow-sm">
                    <div class="card-body text-center">
                        <div class="feature-icon mb-3">
                            <i class="fas fa-heart fa-3x"></i>
                        </div>
                        <h5 class="card-title fw-bold text-primary">ด้วยความเคารพ</h5>
                        <p class="card-text text-muted">บริการที่ให้เกียรติและเคารพในทุกรายละเอียด</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card h-100 shadow-sm">
                    <div class="card-body text-center">
                        <div class="feature-icon mb-3">
                            <i class="fas fa-dove fa-3x"></i>
                        </div>
                        <h5 class="card-title fw-bold text-primary">ความสงบ</h5>
                        <p class="card-text text-muted">สร้างบรรยากาศที่สงบและเหมาะสมในทุกช่วงเวลา</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card h-100 shadow-sm">
                    <div class="card-body text-center">
                        <div class="feature-icon mb-3">
                            <i class="fas fa-hands-helping fa-3x"></i>
                        </div>
                        <h5 class="card-title fw-bold text-primary">การดูแลเอาใจใส่</h5>
                        <p class="card-text text-muted">ให้การดูแลและเอาใจใส่ในทุกรายละเอียดอย่างประณีต</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="py-5">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <h3 class="fw-bold mb-3">พร้อมเริ่มต้นธุรกิจออนไลน์แล้วหรือยัง?</h3>
                <p class="lead mb-0">ติดต่อเราวันนี้เพื่อเริ่มต้นการขายออนไลน์ที่ประสบความสำเร็จ</p>
            </div>
            <div class="col-lg-4 text-lg-end">
                <div class="d-flex gap-2 justify-content-lg-end">
                    <a href="{{ route('contact.index') }}" class="btn btn-primary btn-lg">
                        <i class="fas fa-phone me-2"></i>ติดต่อเรา
                    </a>
                    <a href="/packages" class="btn btn-outline-primary btn-lg">
                        <i class="fas fa-box me-2"></i>แพ็กเกจ
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection
