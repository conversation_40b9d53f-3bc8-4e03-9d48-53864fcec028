

<?php $__env->startSection('title', 'แก้ไขกิจกรรม'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <!-- Header Section -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">
                <i class="fas fa-edit"></i> แก้ไขกิจกรรม
            </h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="<?php echo e(route('admin.dashboard')); ?>">หน้าหลัก</a></li>
                    <li class="breadcrumb-item"><a href="<?php echo e(route('admin.activities.index')); ?>">จัดการกิจกรรม</a></li>
                    <li class="breadcrumb-item active" aria-current="page">แก้ไขกิจกรรม</li>
                </ol>
            </nav>
        </div>
        <a href="<?php echo e(route('admin.activities.index')); ?>" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> กลับ
        </a>
    </div>

    <!-- Alert Messages -->
    <?php if(session('success')): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle"></i> <?php echo e(session('success')); ?>

            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>

    <?php if(session('error')): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle"></i> <?php echo e(session('error')); ?>

            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <div class="row justify-content-center">
                <div class="col-lg-8 col-12">
                    <div class="card shadow-sm border-0">
                        <div class="card-header bg-warning text-dark">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-edit"></i> แก้ไขกิจกรรม: <?php echo e($activity->title); ?>

                            </h5>
                        </div>

                        <form action="<?php echo e(route('admin.activities.update', $activity)); ?>" method="POST" enctype="multipart/form-data" id="activityForm">
                            <?php echo csrf_field(); ?>
                            <?php echo method_field('PUT'); ?>
                            <input type="hidden" name="remove_cover_image" id="removeCoverImageFlag" value="0">

                            <div class="card-body">
                                <!-- Basic Information -->
                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="mb-3">
                                            <label for="title" class="form-label">ชื่อกิจกรรม <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control <?php $__errorArgs = ['title'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                                   id="title" name="title" value="<?php echo e(old('title', $activity->title)); ?>" required>
                                            <?php $__errorArgs = ['title'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="mb-3">
                                            <label for="description" class="form-label">รายละเอียด <span class="text-danger">*</span></label>
                                            <textarea class="form-control <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                                      id="description" name="description" rows="4" required><?php echo e(old('description', $activity->description)); ?></textarea>
                                            <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="category_id" class="form-label">หมวดหมู่ <span class="text-danger">*</span></label>
                                            <select class="form-control <?php $__errorArgs = ['category_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                                    id="category_id" name="category_id" required>
                                                <option value="">เลือกหมวดหมู่</option>
                                                <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <option value="<?php echo e($category->id); ?>"
                                                            <?php echo e(old('category_id', $activity->category_id) == $category->id ? 'selected' : ''); ?>>
                                                        <?php echo e($category->name); ?>

                                                    </option>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </select>
                                            <?php $__errorArgs = ['category_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="activity_date" class="form-label">วันที่จัดกิจกรรม <span class="text-danger">*</span></label>
                                            <input type="date" class="form-control <?php $__errorArgs = ['activity_date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                                   id="activity_date" name="activity_date"
                                                   value="<?php echo e(old('activity_date', $activity->activity_date ? $activity->activity_date->format('Y-m-d') : '')); ?>" required>
                                            <?php $__errorArgs = ['activity_date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="mb-3">
                                            <label for="location" class="form-label">สถานที่</label>
                                            <input type="text" class="form-control <?php $__errorArgs = ['location'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                                   id="location" name="location" value="<?php echo e(old('location', $activity->location)); ?>"
                                                   placeholder="สถานที่จัดกิจกรรม">
                                            <?php $__errorArgs = ['location'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                        </div>
                                    </div>
                                </div>

                                <!-- Cover Image Section -->
                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="mb-3">
                                            <label for="cover_image" class="form-label">รูปปกกิจกรรม</label>
                                            <input type="file" class="form-control <?php $__errorArgs = ['cover_image'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                                   id="cover_image" name="cover_image" accept="image/*">
                                            <div class="form-text">ขนาดไฟล์ไม่เกิน 2MB (JPG, PNG, GIF, WebP)</div>
                                            <?php $__errorArgs = ['cover_image'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>

                                            <!-- Current Image Display -->
                                            <?php if($activity->cover_image): ?>
                                                <div id="imagePreview" class="mt-3">
                                                    <div class="row">
                                                        <div class="col-md-4">
                                                            <img id="previewImg" src="<?php echo e($activity->cover_image_url); ?>"
                                                                 class="img-thumbnail" style="max-width: 100%; height: 200px; object-fit: cover;">
                                                        </div>
                                                        <div class="col-md-8">
                                                            <div id="currentFileName" class="mb-2">
                                                                <small class="text-info">
                                                                    <i class="fas fa-file-image"></i> ไฟล์ปัจจุบัน: <strong><?php echo e($activity->cover_image); ?></strong>
                                                                </small>
                                                            </div>
                                                            <button type="button" class="btn btn-danger btn-sm" id="removeImage">
                                                                <i class="fas fa-trash"></i> ลบรูปภาพ
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                            <?php else: ?>
                                                <div id="noImageDisplay" class="mt-3 text-center p-3 border border-dashed rounded">
                                                    <i class="fas fa-image fa-2x text-muted"></i>
                                                    <p class="text-muted mb-0 mt-2">ไม่มีรูปปก</p>
                                                </div>
                                                <div id="imagePreview" class="mt-3" style="display: none;">
                                                    <div class="row">
                                                        <div class="col-md-4">
                                                            <img id="previewImg" src="" class="img-thumbnail"
                                                                 style="max-width: 100%; height: 200px; object-fit: cover;">
                                                        </div>
                                                        <div class="col-md-8">
                                                            <div id="currentFileName" class="mb-2"></div>
                                                            <button type="button" class="btn btn-danger btn-sm" id="removeImage">
                                                                <i class="fas fa-trash"></i> ลบรูปภาพ
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>

                                <!-- Gallery Images Section -->
                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="mb-3">
                                            <label for="gallery_images" class="form-label">รูปแกลเลอรี่</label>
                                            <input type="file" class="form-control <?php $__errorArgs = ['gallery_images.*'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                                   id="gallery_images" name="gallery_images[]" accept="image/*" multiple>
                                            <div class="form-text">เลือกได้หลายรูป ขนาดไฟล์ไม่เกิน 2MB ต่อรูป</div>
                                            <?php $__errorArgs = ['gallery_images.*'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>

                                            <!-- Existing Gallery Images -->
                                            <?php if($activity->images->count() > 0): ?>
                                                <div class="mt-3">
                                                    <h6>รูปภาพในแกลเลอรี่ปัจจุบัน:</h6>
                                                    <div class="row g-2" id="existingGallery">
                                                        <?php $__currentLoopData = $activity->images; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $image): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                            <div class="col-lg-2 col-md-3 col-4 mb-2" id="gallery-image-<?php echo e($image->id); ?>">
                                                                <div class="position-relative">
                                                                    <img src="<?php echo e($image->image_url); ?>" class="img-thumbnail w-100"
                                                                         style="height: 100px; object-fit: cover;">
                                                                    <button type="button" class="btn btn-sm btn-danger position-absolute top-0 end-0 m-1"
                                                                            onclick="deleteGalleryImage(<?php echo e($image->id); ?>)">
                                                                        <i class="fas fa-times"></i>
                                                                    </button>
                                                                </div>
                                                            </div>
                                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                    </div>
                                                </div>
                                            <?php endif; ?>

                                            <!-- New Images Preview -->
                                            <div id="newGalleryPreview" class="mt-3" style="display: none;">
                                                <h6>รูปภาพใหม่ที่จะเพิ่ม:</h6>
                                                <div class="row g-2" id="newImagesContainer"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Publish Status -->
                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="mb-3">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="is_published" name="is_published" value="1"
                                                       <?php echo e(old('is_published', $activity->is_published) ? 'checked' : ''); ?>>
                                                <label class="form-check-label" for="is_published">
                                                    เผยแพร่กิจกรรม
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="card-footer">
                                <div class="d-flex justify-content-between">
                                    <a href="<?php echo e(route('admin.activities.index')); ?>" class="btn btn-secondary">
                                        <i class="fas fa-times"></i> ยกเลิก
                                    </a>
                                    <button type="submit" class="btn btn-warning">
                                        <i class="fas fa-save"></i> อัปเดตกิจกรรม
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
$(document).ready(function() {
    // Image preview
    $('#cover_image').on('change', function() {
        let file = this.files[0];
        if (file) {
            // Cancel image removal (if any)
            $('#removeCoverImageFlag').val('0');

            let reader = new FileReader();
            reader.onload = function(e) {
                $('#previewImg').attr('src', e.target.result);
                $('#imagePreview').show();
                $('#noImageDisplay').hide();

                // Update filename display
                $('#currentFileName').html(`
                    <small class="text-success">
                        <i class="fas fa-file-image"></i> ไฟล์ใหม่: <strong>${file.name}</strong>
                    </small>
                `);

                // Change button back to remove image
                $('#removeImage').html('<i class="fas fa-trash"></i> ลบรูปภาพ')
                    .removeClass('btn-warning').addClass('btn-danger');
            };
            reader.readAsDataURL(file);
        }
    });

    // Remove cover image
    $('#removeImage').on('click', function() {
        Swal.fire({
            title: 'ลบรูปปก?',
            text: 'คุณแน่ใจหรือไม่ที่จะลบรูปปกนี้?',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#6c757d',
            confirmButtonText: 'ลบเลย',
            cancelButtonText: 'ยกเลิก'
        }).then((result) => {
            if (result.isConfirmed) {
                $('#cover_image').val('');
                $('#imagePreview').hide();
                $('#noImageDisplay').show();
                $('#removeCoverImageFlag').val('1');
            }
        });
    });

    // Gallery images preview
    $('#gallery_images').on('change', function() {
        if (this.files && this.files.length > 0) {
            let previewHtml = '';
            let filesArray = Array.from(this.files);

            filesArray.forEach(function(file, index) {
                let reader = new FileReader();
                reader.onload = function(e) {
                    previewHtml += `
                        <div class="col-lg-2 col-md-3 col-4 mb-2">
                            <div class="position-relative">
                                <img src="${e.target.result}" class="img-thumbnail w-100" style="height: 100px; object-fit: cover;">
                                <span class="badge bg-primary position-absolute top-0 start-0 m-1">ใหม่</span>
                            </div>
                        </div>
                    `;

                    if (index === filesArray.length - 1) {
                        $('#newImagesContainer').html(previewHtml);
                        $('#newGalleryPreview').show();
                    }
                };
                reader.readAsDataURL(file);
            });
        } else {
            $('#newGalleryPreview').hide();
        }
    });
});

function deleteGalleryImage(imageId) {
    Swal.fire({
        title: 'ลบรูปภาพ?',
        text: 'คุณแน่ใจหรือไม่ที่จะลบรูปภาพนี้?',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#6c757d',
        confirmButtonText: 'ลบเลย',
        cancelButtonText: 'ยกเลิก'
    }).then((result) => {
        if (result.isConfirmed) {
            let activityId = <?php echo e($activity->id); ?>;
            $.ajax({
                url: `<?php echo e(url('admin/activities')); ?>/${activityId}/images/${imageId}`,
                type: 'DELETE',
                data: {
                    _token: '<?php echo e(csrf_token()); ?>'
                },
                success: function(response) {
                    if (response.success) {
                        Swal.fire('สำเร็จ', response.message, 'success');
                        // Remove image from preview
                        $(`#gallery-image-${imageId}`).remove();
                    } else {
                        Swal.fire('ข้อผิดพลาด', response.message, 'error');
                    }
                },
                error: function(xhr) {
                    Swal.fire('ข้อผิดพลาด', 'เกิดข้อผิดพลาดในการลบรูปภาพ', 'error');
                }
            });
        }
    });
}
</script>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\XAMPP\htdocs\SoloShop\resources\views/admin/activities/edit.blade.php ENDPATH**/ ?>