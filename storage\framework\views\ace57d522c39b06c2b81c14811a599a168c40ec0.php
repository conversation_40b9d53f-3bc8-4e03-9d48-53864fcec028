<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <title><?php echo $__env->yieldContent('title', isset($siteSettings) ? $siteSettings->site_name : 'SoloShop'); ?></title>

    <!-- Favicon -->
    <?php if(isset($siteSettings) && $siteSettings->site_favicon): ?>
        <link rel="icon" type="image/x-icon" href="<?php echo e(\App\Helpers\ImageHelper::getImageUrl($siteSettings->site_favicon)); ?>">
    <?php else: ?>
        <link rel="icon" type="image/x-icon" href="<?php echo e(asset('favicon.ico')); ?>">
    <?php endif; ?>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Sarabun:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    
    <?php if(request()->is('admin') || request()->is('admin/*')): ?>
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/admin-lte@3.2/dist/css/adminlte.min.css">
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@5.15.4/css/all.min.css">
    <?php endif; ?>
    
    <style>
        body {
            font-family: 'Sarabun', sans-serif;
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 50%, #f8f9fa 100%);
            background-attachment: fixed;
        }

        /* Memorial Theme Colors - Elegant Black & White */
        :root {
            --primary-color: #1a1a1a;
            --secondary-color: #000000;
            --accent-color: #333333;
            --text-dark: #1a1a1a;
            --text-light: #666666;
            --memorial-gold: #c9a96e;
            --memorial-silver: #b8b8b8;
            --pure-white: #ffffff;
            --soft-gray: #f8f9fa;
            --dark-gray: #2c2c2c;
        }
        
        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
            text-shadow: 0 2px 4px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }

        .navbar-brand:hover {
            transform: translateY(-1px);
            text-shadow: 0 4px 8px rgba(201, 169, 110, 0.3);
        }
        
        .hero-section {
            background: linear-gradient(135deg, #000000 0%, #1a1a1a 50%, #2c2c2c 100%);
            min-height: 600px;
            display: flex;
            align-items: center;
            position: relative;
            overflow: hidden;
            border-bottom: 3px solid var(--memorial-gold);
        }
        
        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="0.3" fill="white" opacity="0.03"/><circle cx="25" cy="25" r="0.2" fill="white" opacity="0.02"/><circle cx="75" cy="75" r="0.2" fill="white" opacity="0.02"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            pointer-events: none;
        }

        /* Memorial Theme Enhancements */
        .hero-section::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at center, transparent 0%, rgba(0,0,0,0.3) 100%);
            pointer-events: none;
        }
        
        .hero-section .container {
            position: relative;
            z-index: 1;
        }
        
        .card {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border: 1px solid rgba(201, 169, 110, 0.2);
            border-radius: 15px;
            overflow: hidden;
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(10px);
        }

        .card:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
            border-color: var(--memorial-gold);
        }
        
        .card-img-top {
            transition: transform 0.3s ease;
        }
        
        .card:hover .card-img-top {
            transform: scale(1.05);
        }
        
        .btn {
            border-radius: 25px;
            padding: 10px 25px;
            font-weight: 500;
            transition: all 0.3s ease;
            border: none;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #1a1a1a 0%, #000000 100%);
            border: none;
            color: white;
            position: relative;
            overflow: hidden;
        }

        .btn-primary::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
            transition: left 0.5s;
        }

        .btn-primary:hover::before {
            left: 100%;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #000000 0%, #1a1a1a 100%);
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.3);
            color: white;
        }

        .btn-outline-primary {
            border: 2px solid #1a1a1a;
            color: #1a1a1a;
            background: transparent;
        }

        .btn-outline-primary:hover {
            background: linear-gradient(135deg, #1a1a1a 0%, #000000 100%);
            border-color: var(--memorial-gold);
            transform: translateY(-3px);
            color: white;
        }
        
        .section-title {
            font-weight: 700;
            color: #1a1a1a;
            margin-bottom: 2rem;
            position: relative;
            letter-spacing: 1px;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 3px;
            background: linear-gradient(135deg, var(--memorial-gold) 0%, var(--memorial-silver) 100%);
            border-radius: 2px;
        }
        
        .footer {
            background: linear-gradient(135deg, #000000 0%, #1a1a1a 100%);
            color: white;
            padding: 4rem 0 2rem 0;
            margin-top: 4rem;
            border-top: 3px solid var(--memorial-gold);
            position: relative;
        }

        .footer::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, var(--memorial-gold), transparent);
        }
        
        .social-links a {
            color: white;
            font-size: 1.5rem;
            margin: 0 10px;
            transition: all 0.3s ease;
            display: inline-block;
        }

        .social-links a:hover {
            color: var(--memorial-gold);
            transform: scale(1.2) translateY(-2px);
            text-shadow: 0 4px 8px rgba(201, 169, 110, 0.4);
        }
        
        .navbar-nav .nav-link {
            position: relative;
            transition: color 0.3s ease;
        }
        
        .navbar-nav .nav-link::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 0;
            height: 2px;
            background: linear-gradient(135deg, var(--memorial-gold) 0%, var(--memorial-silver) 100%);
            transition: width 0.3s ease;
        }

        .navbar-nav .nav-link:hover::after {
            width: 100%;
        }
        
        .badge {
            border-radius: 20px;
            padding: 8px 16px;
        }
        
        .text-primary {
            color: var(--primary-color) !important;
        }

        .bg-primary {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%) !important;
        }
        
        .package-description, .service-description, .article-content {
            line-height: 1.8;
            font-size: 1.1rem;
        }
        
        .sticky-top {
            z-index: 1020;
        }
        
        /* Memorial Floating Animation */
        @keyframes floating {
            0% {
                transform: translateY(0px);
            }
            50% {
                transform: translateY(-15px);
            }
            100% {
                transform: translateY(0px);
            }
        }

        .floating-animation {
            animation: floating 6s ease-in-out infinite;
            opacity: 0.9;
        }

        /* Memorial Theme Card Styles */
        .card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(52, 73, 94, 0.1);
        }

        .card:hover {
            background: rgba(255, 255, 255, 1);
            box-shadow: 0 15px 35px rgba(52, 73, 94, 0.15);
        }

        /* Memorial Theme Text Colors */
        .text-primary {
            color: var(--primary-color) !important;
        }

        .bg-primary {
            background-color: var(--primary-color) !important;
        }

        /* Memorial Theme Feature Icons */
        .feature-icon i {
            color: var(--memorial-gold);
            text-shadow: 0 2px 4px rgba(212, 175, 55, 0.3);
        }

        /* Memorial Theme Navigation */
        .navbar-light .navbar-brand {
            color: var(--text-dark) !important;
            font-weight: 600;
        }

        .navbar-light .navbar-nav .nav-link {
            color: var(--text-dark) !important;
            font-weight: 500;
        }

        .navbar-light .navbar-nav .nav-link:hover {
            color: var(--memorial-gold) !important;
        }

        /* Memorial Theme Special Effects */
        .hero-section .display-4 {
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
            letter-spacing: 1px;
        }

        .hero-section .lead {
            text-shadow: 0 1px 2px rgba(0,0,0,0.2);
        }

        /* Memorial Theme Footer */
        .bg-dark {
            background: linear-gradient(135deg, var(--secondary-color) 0%, var(--primary-color) 100%) !important;
        }

        /* Memorial Theme Buttons Enhancement */
        .btn-light {
            background: rgba(255, 255, 255, 0.95);
            border: 1px solid rgba(201, 169, 110, 0.3);
            color: var(--text-dark);
            font-weight: 600;
            position: relative;
            overflow: hidden;
        }

        .btn-light::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(201, 169, 110, 0.1), transparent);
            transition: left 0.5s;
        }

        .btn-light:hover::before {
            left: 100%;
        }

        .btn-light:hover {
            background: var(--memorial-gold);
            color: white;
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(201, 169, 110, 0.4);
            border-color: var(--memorial-gold);
        }

        .btn-outline-light {
            border: 2px solid rgba(255, 255, 255, 0.8);
            color: rgba(255, 255, 255, 0.9);
            background: transparent;
        }

        .btn-outline-light:hover {
            background: rgba(255, 255, 255, 0.15);
            border-color: var(--memorial-gold);
            color: var(--memorial-gold);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255, 255, 255, 0.2);
        }

        /* Gallery Styles */
        .gallery-item {
            position: relative;
            overflow: hidden;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }

        .gallery-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.2);
        }

        .gallery-image-container {
            position: relative;
            overflow: hidden;
            height: 300px;
        }

        .gallery-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
            cursor: pointer;
        }

        .gallery-item:hover .gallery-image {
            transform: scale(1.1);
        }

        .gallery-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(52, 73, 94, 0.8) 0%, rgba(44, 62, 80, 0.9) 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .gallery-item:hover .gallery-overlay {
            opacity: 1;
        }

        .gallery-overlay-content {
            text-align: center;
            padding: 20px;
        }

        .gallery-overlay-content h5 {
            font-weight: 600;
            margin-bottom: 10px;
        }

        .gallery-overlay-content i {
            opacity: 0.8;
            transition: all 0.3s ease;
        }

        .gallery-item:hover .gallery-overlay-content i {
            opacity: 1;
            transform: scale(1.1);
        }

        /* Gallery Modal Styles */
        .modal-content.bg-transparent {
            background: rgba(0,0,0,0.9) !important;
        }

        .btn-close-white {
            filter: invert(1) grayscale(100%) brightness(200%);
        }

        /* High-tech Loading Animation */
        .page-loader {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #000000 0%, #1a1a1a 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            opacity: 1;
            transition: opacity 0.5s ease;
        }

        .page-loader.fade-out {
            opacity: 0;
            pointer-events: none;
        }

        .loader-content {
            text-align: center;
            color: white;
        }

        .loader-spinner {
            width: 60px;
            height: 60px;
            border: 3px solid rgba(201, 169, 110, 0.3);
            border-top: 3px solid var(--memorial-gold);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Scroll Reveal Animation */
        .scroll-reveal {
            opacity: 0;
            transform: translateY(30px);
            transition: all 0.6s ease;
        }

        .scroll-reveal.revealed {
            opacity: 1;
            transform: translateY(0);
        }

        /* Parallax Effect */
        .parallax-bg {
            background-attachment: fixed;
            background-position: center;
            background-repeat: no-repeat;
            background-size: cover;
        }

        @media (max-width: 768px) {
            .hero-section {
                min-height: 400px;
                text-align: center;
            }

            .display-4 {
                font-size: 2.5rem;
            }

            .btn-lg {
                padding: 12px 24px;
                font-size: 1rem;
            }

            /* Gentle floating animation on mobile */
            .floating-animation {
                animation: floating 8s ease-in-out infinite;
            }

            .hero-section .display-4 {
                font-size: 2rem;
                text-shadow: 0 1px 3px rgba(0,0,0,0.3);
            }

            /* Gallery Mobile Styles */
            .gallery-image-container {
                height: 250px;
            }

            .gallery-overlay-content {
                padding: 15px;
            }

            .gallery-overlay-content h5 {
                font-size: 1rem;
            }

            .gallery-overlay-content p {
                font-size: 0.9rem;
            }

            .gallery-overlay-content i {
                font-size: 1.5rem !important;
            }

            /* Disable parallax on mobile */
            .parallax-bg {
                background-attachment: scroll;
            }
        }
    </style>
</head>
<body class="<?php if(request()->is('admin') || request()->is('admin/*')): ?> hold-transition sidebar-mini <?php endif; ?>">
<?php if(request()->is('admin') || request()->is('admin/*')): ?>
    <div class="wrapper">
        <!-- Main Sidebar Container -->
        <aside class="main-sidebar sidebar-dark-primary elevation-4">
            <a href="/admin" class="brand-link text-center">
                <?php if(isset($siteSettings) && $siteSettings->site_logo): ?>
                    <img src="<?php echo e(\App\Helpers\ImageHelper::getImageUrl($siteSettings->site_logo)); ?>"
                         alt="<?php echo e($siteSettings->site_name ?? 'SoloShop'); ?>"
                         class="brand-image img-circle elevation-3"
                         style="max-height: 40px; width: auto;">
                <?php else: ?>
                    <i class="fas fa-cogs fa-2x"></i>
                <?php endif; ?>
                <span class="brand-text font-weight-light">
                    <?php echo e(isset($siteSettings) ? $siteSettings->site_name : 'SoloShop'); ?> Admin
                </span>
            </a>
            <div class="sidebar">
                <!-- User Panel -->
                <div class="user-panel mt-3 pb-3 mb-3 d-flex">
                    <div class="image">
                        <i class="fas fa-user-circle fa-2x text-white"></i>
                    </div>
                    <div class="info">
                        <a href="#" class="d-block text-white"><?php echo e(Auth::user()->name); ?></a>
                        <small class="text-light">Administrator</small>
                    </div>
                </div>

                <nav class="mt-2">
                    <ul class="nav nav-pills nav-sidebar flex-column" data-widget="treeview" role="menu" data-accordion="false">

                        <!-- Dashboard -->
                        <li class="nav-item">
                            <a href="<?php echo e(route('admin.dashboard')); ?>" class="nav-link <?php echo e(request()->is('admin') ? 'active' : ''); ?>">
                                <i class="nav-icon fas fa-tachometer-alt"></i>
                                <p>แดชบอร์ด</p>
                            </a>
                        </li>

                        <!-- จัดการเนื้อหาเว็บไซต์ -->
                        <li class="nav-header">จัดการเนื้อหาเว็บไซต์</li>

                        <li class="nav-item">
                            <a href="<?php echo e(route('admin.homepage.index')); ?>" class="nav-link <?php echo e(request()->is('admin/homepage*') ? 'active' : ''); ?>">
                                <i class="nav-icon fas fa-home"></i>
                                <p>หน้าแรก</p>
                            </a>
                        </li>

                        <li class="nav-item">
                            <a href="<?php echo e(route('admin.services.index')); ?>" class="nav-link <?php echo e(request()->is('admin/services*') ? 'active' : ''); ?>">
                                <i class="nav-icon fas fa-tools"></i>
                                <p>บริการ</p>
                            </a>
                        </li>

                        <li class="nav-item">
                            <a href="<?php echo e(route('admin.packages.index')); ?>" class="nav-link <?php echo e(request()->is('admin/packages*') ? 'active' : ''); ?>">
                                <i class="nav-icon fas fa-box"></i>
                                <p>แพ็กเกจ</p>
                            </a>
                        </li>

                        <li class="nav-item">
                            <a href="<?php echo e(route('admin.activities.index')); ?>" class="nav-link <?php echo e(request()->is('admin/activities*') ? 'active' : ''); ?>">
                                <i class="nav-icon fas fa-images"></i>
                                <p>กิจกรรม</p>
                            </a>
                        </li>

                        <!-- จัดการข้อมูล -->
                        <li class="nav-header">จัดการข้อมูล</li>

                        <li class="nav-item">
                            <a href="<?php echo e(route('admin.contacts.index')); ?>" class="nav-link <?php echo e(request()->is('admin/contacts*') ? 'active' : ''); ?>">
                                <i class="nav-icon fas fa-envelope"></i>
                                <p>
                                    ข้อความติดต่อ
                                    <?php
                                        $unreadCount = \App\Models\Contact::where('is_read', false)->count();
                                    ?>
                                    <?php if($unreadCount > 0): ?>
                                        <span class="badge badge-danger right"><?php echo e($unreadCount); ?></span>
                                    <?php endif; ?>
                                </p>
                            </a>
                        </li>

                        <!-- ระบบ -->
                        <li class="nav-header">ระบบ</li>

                        <li class="nav-item">
                            <a href="<?php echo e(route('admin.settings.index')); ?>" class="nav-link <?php echo e(request()->is('admin/settings*') ? 'active' : ''); ?>">
                                <i class="nav-icon fas fa-cog"></i>
                                <p>การตั้งค่าเว็บไซต์</p>
                            </a>
                        </li>

                        <li class="nav-item">
                            <a href="/" class="nav-link" target="_blank">
                                <i class="nav-icon fas fa-external-link-alt"></i>
                                <p>ดูเว็บไซต์</p>
                            </a>
                        </li>

                        <li class="nav-item">
                            <form method="POST" action="<?php echo e(route('logout')); ?>" class="nav-link" style="border: none; background: none; padding: 0;">
                                <?php echo csrf_field(); ?>
                                <button class="btn btn-link nav-link text-danger" type="submit">
                                    <i class="nav-icon fas fa-sign-out-alt"></i>
                                    <p>ออกจากระบบ</p>
                                </button>
                            </form>
                        </li>
                    </ul>
                </nav>
            </div>
        </aside>
        <!-- Content Wrapper. Contains page content -->
        <div class="content-wrapper" style="min-height: 100vh;">
            <section class="content pt-3">
                <div class="container-fluid">
                    <?php echo $__env->yieldContent('content'); ?>
                </div>
            </section>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/admin-lte@3.2/dist/js/adminlte.min.js"></script>
<?php else: ?>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm sticky-top" style="backdrop-filter: blur(10px); background: rgba(255, 255, 255, 0.95) !important; border-bottom: 1px solid rgba(201, 169, 110, 0.2);"
        <div class="container">
            <a class="navbar-brand text-primary" href="/">
                <?php if(isset($siteSettings) && $siteSettings->site_logo): ?>
                    <img src="<?php echo e(\App\Helpers\ImageHelper::getImageUrl($siteSettings->site_logo)); ?>"
                         alt="<?php echo e($siteSettings->site_name ?? 'บริการงานศพครบวงจร'); ?>"
                         style="height: 40px; width: auto;" class="me-2">
                <?php else: ?>
                    <i class="fas fa-dove me-2" style="color: var(--memorial-gold);"></i>
                <?php endif; ?>
                <?php echo e(isset($siteSettings) ? $siteSettings->site_name : 'บริการงานศพครบวงจร'); ?>

            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link fw-medium" href="<?php echo e(route('home')); ?>">
                            <i class="fas fa-home me-1"></i>หน้าแรก
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link fw-medium" href="<?php echo e(route('services.index')); ?>">
                            <i class="fas fa-hands-helping me-1"></i>บริการของเรา
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link fw-medium" href="<?php echo e(route('packages.index')); ?>">
                            <i class="fas fa-box-heart me-1"></i>แพ็กเกจบริการ
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link fw-medium" href="<?php echo e(route('activities.index')); ?>">
                            <i class="fas fa-images me-1"></i>ภาพกิจกรรม
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link fw-medium" href="<?php echo e(route('contact.index')); ?>">
                            <i class="fas fa-phone me-1"></i>ติดต่อเรา
                        </a>
                    </li>
                    <?php if(auth()->guard()->check()): ?>
                        <li class="nav-item">
                            <a class="nav-link fw-medium text-success" href="<?php echo e(route('admin.dashboard')); ?>">
                                <i class="fas fa-cogs me-1"></i>หลังบ้าน
                            </a>
                        </li>
                        <li class="nav-item">
                            <form method="POST" action="<?php echo e(route('logout')); ?>" class="d-inline">
                                <?php echo csrf_field(); ?> 
                                <button class="btn btn-link nav-link text-danger fw-medium" type="submit">
                                    <i class="fas fa-sign-out-alt me-1"></i>ออกจากระบบ
                                </button>
                            </form>
                        </li>
                    <?php else: ?>
                        <li class="nav-item">
                            <a class="nav-link fw-medium" href="<?php echo e(route('login')); ?>">
                                <i class="fas fa-sign-in-alt me-1"></i>เข้าสู่ระบบ
                            </a>
                        </li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main>
        <?php echo $__env->yieldContent('content'); ?>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="row">
                <div class="col-md-4">
                    <h5 class="mb-3" style="color: var(--memorial-gold);">
                        <i class="fas fa-dove me-2"></i>บริการงานศพครบวงจร
                    </h5>
                    <p>ให้การดูแลและเอาใจใส่ในทุกรายละเอียด ด้วยความเคารพและเกียรติ ในช่วงเวลาที่สำคัญที่สุด</p>
                </div>
                <div class="col-md-4">
                    <h5 class="mb-3" style="color: var(--memorial-gold);">เมนูหลัก</h5>
                    <ul class="list-unstyled">
                        <li class="mb-2"><a href="/services" class="text-light text-decoration-none"><i class="fas fa-hands-helping me-2"></i>บริการของเรา</a></li>
                        <li class="mb-2"><a href="/packages" class="text-light text-decoration-none"><i class="fas fa-box-heart me-2"></i>แพ็กเกจบริการ</a></li>
                        <li class="mb-2"><a href="/activities" class="text-light text-decoration-none"><i class="fas fa-images me-2"></i>ภาพกิจกรรม</a></li>
                        <li class="mb-2"><a href="/contact" class="text-light text-decoration-none"><i class="fas fa-phone me-2"></i>ติดต่อเรา</a></li>
                    </ul>
                </div>
                <div class="col-md-4">
                    <h5 class="mb-3" style="color: var(--memorial-gold);">ติดต่อเรา</h5>
                    <p><i class="fas fa-phone me-2" style="color: var(--memorial-gold);"></i><?php echo e(isset($siteSettings) ? $siteSettings->contact_phone : '02-123-4567'); ?></p>
                    <p><i class="fas fa-envelope me-2" style="color: var(--memorial-gold);"></i><?php echo e(isset($siteSettings) ? $siteSettings->contact_email : '<EMAIL>'); ?></p>
                    <p><i class="fas fa-map-marker-alt me-2" style="color: var(--memorial-gold);"></i><?php echo e(isset($siteSettings) ? $siteSettings->contact_address : 'กรุงเทพมหานคร'); ?></p>
                    <div class="social-links mt-3">
                        <?php if(isset($siteSettings) && $siteSettings->facebook_url): ?>
                            <a href="<?php echo e($siteSettings->facebook_url); ?>" target="_blank"><i class="fab fa-facebook"></i></a>
                        <?php endif; ?>
                        <?php if(isset($siteSettings) && $siteSettings->line_url): ?>
                            <a href="<?php echo e($siteSettings->line_url); ?>" target="_blank"><i class="fab fa-line"></i></a>
                        <?php endif; ?>
                        <?php if(isset($siteSettings) && $siteSettings->instagram_url): ?>
                            <a href="<?php echo e($siteSettings->instagram_url); ?>" target="_blank"><i class="fab fa-instagram"></i></a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            <hr class="my-4" style="border-color: var(--memorial-gold); opacity: 0.3;">
            <div class="text-center">
                <p class="mb-0">&copy; 2024 <?php echo e(isset($siteSettings) ? $siteSettings->site_name : 'บริการงานศพครบวงจร'); ?>. ให้บริการด้วยความเคารพและเกียรติ</p>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<?php endif; ?>
</body>
</html> <?php /**PATH D:\XAMPP\htdocs\SoloShop\resources\views/layouts/app.blade.php ENDPATH**/ ?>